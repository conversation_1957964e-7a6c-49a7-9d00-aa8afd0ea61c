import puppeteer from 'puppeteer';

async function testAuthenticationSystem() {
  console.log('🚨 HOLY RULES 1-12 NUCLEAR CHECKLIST ANNOUNCEMENT 🚨');
  console.log('Testing authentication system with comprehensive verification...');
  
  const browser = await puppeteer.launch({ 
    headless: false,
    defaultViewport: { width: 1200, height: 800 },
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  const page = await browser.newPage();
  
  try {
    // Navigate to homepage
    console.log('📍 Navigating to localhost:5002...');
    await page.goto('http://localhost:5002', { waitUntil: 'networkidle2' });
    
    // Take initial screenshot
    await page.screenshot({ path: 'auth-test-1-homepage.png', fullPage: true });
    console.log('✅ Screenshot 1: Homepage loaded');
    
    // Look for Sign In button
    console.log('🔍 Looking for Sign In button...');
    const signInButton = await page.$('button:has-text("Sign In")') || 
                        await page.$('button[class*="signin"]') ||
                        await page.$('text=Sign In');
    
    if (!signInButton) {
      // Try alternative selectors
      const buttons = await page.$$('button');
      console.log(`Found ${buttons.length} buttons on page`);
      
      for (let i = 0; i < buttons.length; i++) {
        const buttonText = await buttons[i].evaluate(el => el.textContent?.trim());
        console.log(`Button ${i}: "${buttonText}"`);
        if (buttonText?.includes('Sign In') || buttonText?.includes('Sign in')) {
          console.log('✅ Found Sign In button via text search');
          await buttons[i].click();
          break;
        }
      }
    } else {
      console.log('✅ Found Sign In button directly');
      await signInButton.click();
    }
    
    // Wait for modal to appear
    console.log('⏳ Waiting for authentication modal...');
    await page.waitForTimeout(2000);
    
    // Take screenshot of modal
    await page.screenshot({ path: 'auth-test-2-modal.png', fullPage: true });
    console.log('✅ Screenshot 2: Authentication modal');
    
    // Check if modal is visible
    const modal = await page.$('[role="dialog"]') || 
                  await page.$('.modal') ||
                  await page.$('[aria-modal="true"]');
    
    if (modal) {
      console.log('✅ Authentication modal is visible');
      
      // Look for email and password fields
      const emailField = await page.$('input[type="email"]') || 
                         await page.$('input[placeholder*="email"]') ||
                         await page.$('input[name="email"]');
      
      const passwordField = await page.$('input[type="password"]') || 
                            await page.$('input[placeholder*="password"]') ||
                            await page.$('input[name="password"]');
      
      if (emailField && passwordField) {
        console.log('✅ Email and password fields found');
        
        // Test with provided credentials
        console.log('🔐 Testing with credentials: <EMAIL>');
        await emailField.type('<EMAIL>');
        await passwordField.type('J4913836j');
        
        // Take screenshot with filled form
        await page.screenshot({ path: 'auth-test-3-filled-form.png', fullPage: true });
        console.log('✅ Screenshot 3: Form filled with credentials');
        
        // Look for submit button
        const submitButton = await page.$('button[type="submit"]') ||
                            await page.$('button:has-text("Sign In")') ||
                            await page.$('button:has-text("Login")');
        
        if (submitButton) {
          console.log('🚀 Attempting to submit login form...');
          await submitButton.click();
          
          // Wait for response
          await page.waitForTimeout(3000);
          
          // Take screenshot of result
          await page.screenshot({ path: 'auth-test-4-login-result.png', fullPage: true });
          console.log('✅ Screenshot 4: Login attempt result');
          
          // Check for success or error messages
          const successMessage = await page.$('.success') || 
                                 await page.$('[class*="success"]') ||
                                 await page.$('text=Welcome') ||
                                 await page.$('text=signed in');
          
          const errorMessage = await page.$('.error') || 
                               await page.$('[class*="error"]') ||
                               await page.$('text=Invalid') ||
                               await page.$('text=failed');
          
          if (successMessage) {
            console.log('✅ SUCCESS: Login appears successful');
          } else if (errorMessage) {
            const errorText = await errorMessage.evaluate(el => el.textContent);
            console.log('❌ ERROR: Login failed with message:', errorText);
          } else {
            console.log('⚠️  UNCLEAR: No clear success/error indication');
          }
          
        } else {
          console.log('❌ ERROR: Submit button not found');
        }
        
      } else {
        console.log('❌ ERROR: Email or password fields not found');
        console.log('Email field:', !!emailField);
        console.log('Password field:', !!passwordField);
      }
      
    } else {
      console.log('❌ ERROR: Authentication modal not visible');
    }
    
    // Test Sign Up functionality
    console.log('\n📝 Testing Sign Up functionality...');
    
    // Look for Sign Up button or toggle
    const signUpButton = await page.$('button:has-text("Sign Up")') ||
                        await page.$('button:has-text("Get Started")') ||
                        await page.$('text=Sign Up') ||
                        await page.$('text=Get Started');
    
    if (signUpButton) {
      console.log('✅ Found Sign Up button');
      await signUpButton.click();
      await page.waitForTimeout(2000);
      
      await page.screenshot({ path: 'auth-test-5-signup-modal.png', fullPage: true });
      console.log('✅ Screenshot 5: Sign Up modal');
      
      // Check for additional fields (full name, etc.)
      const fullNameField = await page.$('input[name="fullName"]') ||
                            await page.$('input[placeholder*="name"]') ||
                            await page.$('input[name="full_name"]');
      
      if (fullNameField) {
        console.log('✅ Full name field found in signup form');
      } else {
        console.log('⚠️  Full name field not found');
      }
      
    } else {
      console.log('❌ Sign Up button not found');
    }
    
    console.log('\n🎯 AUTHENTICATION SYSTEM TEST COMPLETE');
    console.log('📊 Results saved as auth-test-*.png screenshots');
    
  } catch (error) {
    console.error('❌ Test failed:', error);
    await page.screenshot({ path: 'auth-test-error.png', fullPage: true });
  } finally {
    await browser.close();
  }
}

// Run the test
testAuthenticationSystem().catch(console.error);
